{"3x3x3 Cube": {"name": "3x3x3 Cube", "category": "NxNxN Cube", "event_type": "WCA", "methods": {"CFOP": {"name": "CFOP", "description": "Cross, F2L, OLL, PLL - The most popular speedcubing method", "steps": {"OLL": {"name": "Orientation of Last Layer", "description": "Algorithms for orienting all pieces on the last layer", "cases": [{"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(R U2 R') (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["R' U' F R' F' R2 U R f' U' f"], "move_count": 12, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "F (R U R' U') F' f (R U R' U') f'", "alternative_algorithms": ["y r U r' U2 R U2 R' U2 r U' r'"], "move_count": 12, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "y' f (R U R' U') f' (U') F (R U R' U') F'", "alternative_algorithms": ["r' R2 U R' U r U2 r' U M'"], "move_count": 14, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "y' f (R U R' U') f' (U) F (R U R' U') F'", "alternative_algorithms": ["l L2 U' L U' l' U2 l U' M'"], "move_count": 14, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(R U R' U) (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["y2 F R' F' R U S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "y (R U2 R') (R' F R F') U2 M' (U R U' r')", "alternative_algorithms": ["r U R' U R U2 r2 U' R U' R' U2 r"], "move_count": 14, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "M U (R U R' U') M' (R' F R F')", "alternative_algorithms": ["r' R U R U R' U' r R2 F R F'"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_dot_case", "name": "NxNxN Cube Dot Case", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(r U R' U') M2 (U R U' R') U' M'", "alternative_algorithms": ["M' U2 M U2 M' U M U2 M' U2 M"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_square_shape", "name": "NxNxN Cube Square Shape", "case_name": "Square Shape", "category": "NxNxN Cube", "algorithm": "r' U2 (R U R' U) r", "alternative_algorithms": ["y2 l' U2 L U L' U l"], "move_count": 7, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_square_shape", "name": "NxNxN Cube Square Shape", "case_name": "Square Shape", "category": "NxNxN Cube", "algorithm": "r <PERSON> (R' U' R U') r'", "alternative_algorithms": ["y2 l U2 L' U' L U' l'"], "move_count": 7, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_lightning_shape", "name": "NxNxN Cube Lightning Shape", "case_name": "Lightning Shape", "category": "NxNxN Cube", "algorithm": "r (U R' U R) U2 r'", "alternative_algorithms": ["r U r' U R U' R' r U' r'"], "move_count": 7, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_lightning_shape", "name": "NxNxN Cube Lightning Shape", "case_name": "Lightning Shape", "category": "NxNxN Cube", "algorithm": "y2 r' (U' R U' R') U2 r", "alternative_algorithms": ["l' U' L U' L' U2 l"], "move_count": 8, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_lightning_shape", "name": "NxNxN Cube Lightning Shape", "case_name": "Lightning Shape", "category": "NxNxN Cube", "algorithm": "M (R U R' U R U2 R') U M'", "alternative_algorithms": ["y2 r U R' U R' F R F' R U2 r'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_lightning_shape", "name": "NxNxN Cube Lightning Shape", "case_name": "Lightning Shape", "category": "NxNxN Cube", "algorithm": "y' M' (<PERSON>' <PERSON>' R U' R' U2 R) U' M", "alternative_algorithms": ["y l L2 U' L U' L' U2 L U' M'"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_lightning_shape", "name": "NxNxN Cube Lightning Shape", "case_name": "Lightning Shape", "category": "NxNxN Cube", "algorithm": "y L F' (L' U' L U) F U' L'", "alternative_algorithms": ["y' f' L F L' U' L' U L S"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_lightning_shape", "name": "NxNxN Cube Lightning Shape", "case_name": "Lightning Shape", "category": "NxNxN Cube", "algorithm": "y R' F (R U R' U') F' U R", "alternative_algorithms": ["y' f R' F' R U R U' R' S'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_fish_shape", "name": "NxNxN Cube Fish Shape", "case_name": "Fish Shape", "category": "NxNxN Cube", "algorithm": "y (R U R' U') (R' F R) (R U R' U') F'", "alternative_algorithms": ["R U2 R' U' S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_fish_shape", "name": "NxNxN Cube Fish Shape", "case_name": "Fish Shape", "category": "NxNxN Cube", "algorithm": "(R U R' U) (R' F R F') (R U2 R')", "alternative_algorithms": ["y F U F' R' F R U' R' F' R"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_fish_shape", "name": "NxNxN Cube Fish Shape", "case_name": "Fish Shape", "category": "NxNxN Cube", "algorithm": "(R U2 R') (R' F R F') (R U2 R')", "alternative_algorithms": ["f R U R' U' f' R U R' U R U2 R'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_fish_shape", "name": "NxNxN Cube Fish Shape", "case_name": "Fish Shape", "category": "NxNxN Cube", "algorithm": "F R (U' R' U') (R U R') F'", "alternative_algorithms": ["F R U' R' U' R U R' F'"], "move_count": 9, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_knight_move_shape", "name": "NxNxN Cube Knight Move Shape", "case_name": "Knight Move Shape", "category": "NxNxN Cube", "algorithm": "(r U' r') U' (r U r') (F' U F)", "alternative_algorithms": ["F U R U' R2 F' R U R U' R'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_knight_move_shape", "name": "NxNxN Cube Knight Move Shape", "case_name": "Knight Move Shape", "category": "NxNxN Cube", "algorithm": "R' F (R U R') F' R (F U' F')", "alternative_algorithms": ["r U R' U' r' F R2 U R' U' F'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_knight_move_shape", "name": "NxNxN Cube Knight Move Shape", "case_name": "Knight Move Shape", "category": "NxNxN Cube", "algorithm": "(r' U' r) (R' U' R U) (r' U r)", "alternative_algorithms": ["y2 l' U' l L' U' L U l' U l"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_knight_move_shape", "name": "NxNxN Cube Knight Move Shape", "case_name": "Knight Move Shape", "category": "NxNxN Cube", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["y2 R' F R U R' U' F' R U' R' U2 R"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_c_shape", "name": "NxNxN Cube C Shape", "case_name": "C <PERSON>", "category": "NxNxN Cube", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["y f R f' U' r' U' R U M'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_c_shape", "name": "NxNxN Cube C Shape", "case_name": "C <PERSON>", "category": "NxNxN Cube", "algorithm": "R' U' (R' F R F') U R", "alternative_algorithms": ["R' F' U' F R U' R' U2 R"], "move_count": 8, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_awkward_shape", "name": "NxNxN Cube Awkward Shape", "case_name": "<PERSON><PERSON><PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "y (R U R') U' (R U' R') (F' U' F) (R U R')", "alternative_algorithms": ["r2 D' r U r' D r2 U' r' U' r"], "move_count": 14, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_awkward_shape", "name": "NxNxN Cube Awkward Shape", "case_name": "<PERSON><PERSON><PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "y2 F U (R U2 R') U' (R U2 R') U' F'", "alternative_algorithms": ["y2 F R' F R2 U' R' U' R U R' F2"], "move_count": 12, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_awkward_shape", "name": "NxNxN Cube Awkward Shape", "case_name": "<PERSON><PERSON><PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "y2 (R U R' U) (R U2 R') F (R U R' U') F'", "alternative_algorithms": ["y2 F U R2 D R' U' R D' R2 F'"], "move_count": 14, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_awkward_shape", "name": "NxNxN Cube Awkward Shape", "case_name": "<PERSON><PERSON><PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "(R' U' R U') (R' U2 R) F (R U R' U') F'", "alternative_algorithms": ["y F R' F' R U2 R' U' R2 U' R2 U2 R"], "move_count": 13, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_all_corners_oriented", "name": "NxNxN Cube All Corners Oriented", "case_name": "All Corners Oriented", "category": "NxNxN Cube", "algorithm": "(r U R' U') M (U R U' R')", "alternative_algorithms": ["R' F R S R' F' R S'"], "move_count": 9, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_all_corners_oriented", "name": "NxNxN Cube All Corners Oriented", "case_name": "All Corners Oriented", "category": "NxNxN Cube", "algorithm": "(R U R' U') M' (U R U' r')", "alternative_algorithms": ["y R U' R' S' R U R' S"], "move_count": 9, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_l_shapes", "name": "NxNxN Cube L Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "F' (L' U' L U) (L' U' L U) F", "alternative_algorithms": ["y' F R' F' R U2 R U' R' U R U2 R'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_l_shapes", "name": "NxNxN Cube L Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "F (R U R' U') (R U R' U') F'", "alternative_algorithms": ["F R' F' U2 R U R' U R2 U2 R'"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_l_shapes", "name": "NxNxN Cube L Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "y2 r U' (r2 U) (r2 U) (r2) U' r", "alternative_algorithms": ["l U' l2 U l2 U l2 U' l"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_l_shapes", "name": "NxNxN Cube L Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "r' U (r2 U') (r2 U') (r2) U r'", "alternative_algorithms": ["y2 l' U l2 U' l2 U' l2 U l'"], "move_count": 9, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_l_shapes", "name": "NxNxN Cube L Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(r' U' R U') (R' U R U') (R' U2 r)", "alternative_algorithms": ["y2 l' U' L U' L' U L U' L' U2 l"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_l_shapes", "name": "NxNxN Cube L Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(r U R' U) (R U' R' U) (R U2 r')", "alternative_algorithms": ["y2 l U L' U L U' L' U L U2 l'"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_line_shapes", "name": "NxNxN Cube Line Shapes", "case_name": "Line Shapes", "category": "NxNxN Cube", "algorithm": "f (R U R' U') (R U R' U') f'", "alternative_algorithms": ["F' U' L' U L U' L' U L F"], "move_count": 10, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_line_shapes", "name": "NxNxN Cube Line Shapes", "case_name": "Line Shapes", "category": "NxNxN Cube", "algorithm": "y2 R' (F' U' F U') (R U R' U) R", "alternative_algorithms": ["R U R' U R U' B U' B' R'"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_line_shapes", "name": "NxNxN Cube Line Shapes", "case_name": "Line Shapes", "category": "NxNxN Cube", "algorithm": "R U2 R2 (U' R U' R') U2 (F R F')", "alternative_algorithms": ["y R' F U R U' R2 F' R2 U R' U' R"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_line_shapes", "name": "NxNxN Cube Line Shapes", "case_name": "Line Shapes", "category": "NxNxN Cube", "algorithm": "(r U r') (U R U' R') (U R U' R') (r U' r')", "alternative_algorithms": ["F R U R' U' R F' r U R' U' r'"], "move_count": 14, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "(R U R' U) (R U' R' U) (R U2 R')", "alternative_algorithms": ["y F R U R' U' R U R' U' R U R' U' F'"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "R U2 (R2' U') (R2 U') (R2' U') U' R", "alternative_algorithms": ["R' U2 R2 U R2 U R2 U2 R'"], "move_count": 10, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "R2 D (R' U2 R) D' (R' U2 R')", "alternative_algorithms": ["y2 R2 D' R U2 R' D R U2 R"], "move_count": 9, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "(r U R' U') (r' F R F')", "alternative_algorithms": ["L F R' F' L' F R F'"], "move_count": 8, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "y (F' r U R') (U' r' F R)", "alternative_algorithms": ["R U2 R D R' U2 R D' R2"], "move_count": 9, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "y R <PERSON> (R' U' R U') R'", "alternative_algorithms": ["R' U' R U' R' U2 R"], "move_count": 8, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_ocll", "name": "NxNxN Cube OCLL", "case_name": "OCLL", "category": "NxNxN Cube", "algorithm": "(R U R' U) (R U2 R')", "alternative_algorithms": ["y2 L U L' U L U2 L'"], "move_count": 7, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_p_shapes", "name": "NxNxN Cube P Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(R' U' F) (U R U' R') F' R", "alternative_algorithms": ["y' F R' F' R U R U R' U' R U' R'"], "move_count": 9, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_p_shapes", "name": "NxNxN Cube P Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "S (R U R' U') (R' F R f')", "alternative_algorithms": ["y2 L U F' U' L' U L F L'"], "move_count": 9, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_p_shapes", "name": "NxNxN Cube P Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "y R' U' (F' U F) R", "alternative_algorithms": ["y2 F' U' L' U L F"], "move_count": 7, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_p_shapes", "name": "NxNxN Cube P Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "f (R U R' U') f'", "alternative_algorithms": ["y2 F U R U' R' F'"], "move_count": 6, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_t_shapes", "name": "NxNxN Cube T Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "(R U R' U') (R' F R F')", "alternative_algorithms": ["y2 L' U' L U L F' L' F"], "move_count": 8, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_t_shapes", "name": "NxNxN Cube T Shapes", "case_name": "<PERSON>", "category": "NxNxN Cube", "algorithm": "F (R U R' U') F'", "alternative_algorithms": ["y2 F' L' U' L U F"], "move_count": 6, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_w_shape", "name": "NxNxN Cube W Shape", "case_name": "<PERSON>pe", "category": "NxNxN Cube", "algorithm": "y2 (L' U' L U') (L' U L U) (L F' L' F)", "alternative_algorithms": ["y2 L' U' L U' L' U L U L F' L' F"], "move_count": 13, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_w_shape", "name": "NxNxN Cube W Shape", "case_name": "<PERSON>pe", "category": "NxNxN Cube", "algorithm": "(R U R' U) (R U' R' U') (R' F R F')", "alternative_algorithms": ["y2 L' U2 l' D' l U2 l' D l L"], "move_count": 12, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}]}, "PLL": {"name": "Permutation of Last Layer", "description": "Algorithms for permuting all pieces on the last layer", "cases": [{"id": "pll_aa_perm", "name": "NxNxN Cube Aa Perm", "case_name": "Aa Perm", "category": "NxNxN Cube", "algorithm": "x (R' U R') D2 (R U' R') D2 R2 x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ab_perm", "name": "NxNxN Cube Ab Perm", "case_name": "<PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "x R2 D2 (R U R') D2 (R U' R) x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_e_perm_", "name": "NxNxN Cube E Perm ", "case_name": "E Perm ", "category": "NxNxN Cube", "algorithm": "y x' (R U' R' D) (R U R' D') (R U R' D) (R U' R' D') x", "alternative_algorithms": [], "move_count": 19, "difficulty": 4, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_f_perm_", "name": "NxNxN Cube F Perm ", "case_name": "F Perm ", "category": "NxNxN Cube", "algorithm": "y (R' U' F') (R U R' U') R' F R2 (U' R' U') (R U R' U) R", "alternative_algorithms": [], "move_count": 19, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ga_perm_", "name": "NxNxN Cube Ga Perm ", "case_name": "Ga Perm ", "category": "NxNxN Cube", "algorithm": "R2 (U R' U R' U' R U') R2 D (U' R' U R) D'", "alternative_algorithms": ["y R U R' F' R U R' U' R' F R U' R' F R2 U' R' U' R U R' F'"], "move_count": 15, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gb_perm", "name": "NxNxN Cube Gb Perm", "case_name": "Gb Perm", "category": "NxNxN Cube", "algorithm": "(R' U' R U) D' R2 (U R' U R U' R U') R2 D", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gc_perm_", "name": "NxNxN Cube Gc Perm ", "case_name": "Gc Perm ", "category": "NxNxN Cube", "algorithm": "R2 (U' R U' R U R' U) R2 D' (U R U' R') D", "alternative_algorithms": ["y2 R2 F2 R U2 R U2 R' F R U R' U' R' F R2"], "move_count": 15, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gd_perm", "name": "NxNxN Cube Gd Perm", "case_name": "Gd Perm", "category": "NxNxN Cube", "algorithm": "(R U R' U') D R2 (U' R U' R' U R' U) R2 D'", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_h_perm", "name": "NxNxN Cube H Perm", "case_name": "H Perm", "category": "NxNxN Cube", "algorithm": "(M2 U' M2) U2 (M2 U' M2)", "alternative_algorithms": [], "move_count": 7, "difficulty": 1, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ja_perm", "name": "NxNxN Cube Ja Perm", "case_name": "<PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "y (R' U L') U2 (R U' R') U2 R L", "alternative_algorithms": ["y R' U L' U2 R U' R' U2 R L"], "move_count": 11, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_jb_perm", "name": "NxNxN Cube Jb Perm", "case_name": "<PERSON><PERSON>", "category": "NxNxN Cube", "algorithm": "(R U R' F') (R U R' U') R' F R2 U' R'", "alternative_algorithms": [], "move_count": 13, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_na_perm", "name": "NxNxN Cube Na Perm", "case_name": "Na Perm", "category": "NxNxN Cube", "algorithm": "(R U R' U) (R U R' F') (R U R' U') R' F R2 U' R' U2 (R U' R')", "alternative_algorithms": [], "move_count": 21, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_nb_perm", "name": "NxNxN Cube Nb Perm", "case_name": "Nb Perm", "category": "NxNxN Cube", "algorithm": "(R' U R U' R') (F' U' F) (R U R') (F R' F') (R U' R)", "alternative_algorithms": ["r' D' F r U' r' F' D r2 U r' U' r' F r F'"], "move_count": 17, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ra_perm", "name": "NxNxN Cube Ra Perm", "case_name": "Ra <PERSON>", "category": "NxNxN Cube", "algorithm": "y (R U' R' U') (R U R D) (R' U' R D') (R' U2 R')", "alternative_algorithms": [], "move_count": 16, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_rb_perm", "name": "NxNxN Cube Rb Perm", "case_name": "Rb <PERSON>", "category": "NxNxN Cube", "algorithm": "(R' <PERSON>) (R U2) (R' F R) (U R' U' R') F' R2", "alternative_algorithms": ["y R2 F R U R U' R' F' R U2 R' U2 R"], "move_count": 13, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_t_perm", "name": "NxNxN Cube T Perm", "case_name": "T Perm", "category": "NxNxN Cube", "algorithm": "(R U R' U') (R' F R2) (U' R' U') (R U R' F')", "alternative_algorithms": [], "move_count": 14, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ua_perm", "name": "NxNxN Cube Ua Perm", "case_name": "Ua Perm", "category": "NxNxN Cube", "algorithm": "y2 (M2 U M) U2 (M' U M2)", "alternative_algorithms": ["R U R' U R' U' R2 U' R' U R' U R"], "move_count": 8, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ub_perm", "name": "NxNxN Cube Ub Perm", "case_name": "Ub Perm", "category": "NxNxN Cube", "algorithm": "y2 (M2 U' M) U2 (M' U' M2)", "alternative_algorithms": ["R2' U R U R' U' R3 U' R' U R'"], "move_count": 8, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_v_perm", "name": "NxNxN Cube V Perm", "case_name": "V Perm", "category": "NxNxN Cube", "algorithm": "(R' U R' U') (R D' R' D) (R' U D') (R2 U' R2) D R2", "alternative_algorithms": ["R' U R U' x' U R U2 R' U' R U' R' U2 R U R' U'"], "move_count": 16, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_y_perm", "name": "NxNxN Cube Y Perm", "case_name": "Y Perm", "category": "NxNxN Cube", "algorithm": "F R (U' R' U') (R U R' F') (R U R' U') (R' F R F')", "alternative_algorithms": [], "move_count": 17, "difficulty": 3, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_z_perm", "name": "NxNxN Cube Z Perm", "case_name": "Z Perm", "category": "NxNxN Cube", "algorithm": "(M2 U) (M2 U) (M' U2) M2 (U2 M')", "alternative_algorithms": [], "move_count": 9, "difficulty": 2, "description": "NxNxN Cube pattern", "source": "https://speedcubedb.com/a/3x3/PLL"}]}, "F2L": {"name": "First Two Layers", "description": "Algorithms for solving the first two layers simultaneously", "cases": [{"id": "f2l_1", "name": "Case 1", "case_name": "1", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 1", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U R U' R'", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "F' r U r'", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "U L U' L'", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "U f R' f'", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_2", "name": "Case 2", "case_name": "2", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 2", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F R' F' R", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "U' L' U L", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "l U L' U' M'", "alternative_algorithms": [], "move_count": 5}, "Back-Right": {"algorithm": "U' R' U R", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_3", "name": "Case 3", "case_name": "3", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 3", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F' U' F", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "L' U' L", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "y R' U' R", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "R' U' R", "alternative_algorithms": [], "move_count": 3}}}, {"id": "f2l_4", "name": "Case 4", "case_name": "4", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 4", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R'", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "F U F'", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "L U L'", "alternative_algorithms": [], "move_count": 3}, "Back-Right": {"algorithm": "f R f'", "alternative_algorithms": [], "move_count": 3}}}, {"id": "f2l_5", "name": "Case 5", "case_name": "5", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 5", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U R' U2 R U' R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U R' F r U' r' F' R", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U2 L U' L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U' R' F R U R' U' F' R", "alternative_algorithms": [], "move_count": 9}}}, {"id": "f2l_6", "name": "Case 6", "case_name": "6", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 6", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' r U' R' U R U r'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L U2 L' U L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U r U' r' U' L U F L'", "alternative_algorithms": [], "move_count": 9}, "Back-Right": {"algorithm": "U R' U' R U2 R' U R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_7", "name": "Case 7", "case_name": "7", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 7", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U2 R' U' R U2 R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "F U R U2 R' U F'", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "U' L U2 L' U2 L U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "r U2 R2 U' R2 U' r", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_8", "name": "Case 8", "case_name": "8", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 8", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "d R' U2 R U R' U2 R", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U2 L U L' U2 L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "l' U2 L2 U L2 U l", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "U R' U2 R U R' U2 R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_9", "name": "Case 9", "case_name": "9", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 9", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U' R' U F' U' F", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L U' L' U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "y U R' U' R U' R' U' R", "alternative_algorithms": [], "move_count": 9}, "Back-Right": {"algorithm": "U R' U' R U' R' U' R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_10", "name": "Case 10", "case_name": "10", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 10", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U R' U R U R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U L U' F U F", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U' f R f'", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_11", "name": "Case 11", "case_name": "11", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 11", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U2 R' U F' U' F", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "L' U L U' L' U L U2 L' U L", "alternative_algorithms": [], "move_count": 11}, "Back-Left": {"algorithm": "U' L U2 L' U f' L' f", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "R' U R U' R' U R U2 R' U R", "alternative_algorithms": [], "move_count": 11}}}, {"id": "f2l_12", "name": "Case 12", "case_name": "12", "category": "NxNxN Cube", "difficulty": 2, "description": "F2L case 12", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U' R' U R U' R' U2 R U' R'", "alternative_algorithms": [], "move_count": 11}, "Front-Left": {"algorithm": "U L' U2 L U' F U F'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L' U2 L2 U L2 U L", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "U R' U2 R U' f R f'", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_13", "name": "Case 13", "case_name": "13", "category": "NxNxN Cube", "difficulty": 2, "description": "F2L case 13", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "y' U R' U R U' R' U' R", "alternative_algorithms": [], "move_count": 9}, "Front-Left": {"algorithm": "U L' U L U' L' U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "d L' U L U' L' U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U' R' U' R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_14", "name": "Case 14", "case_name": "14", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 14", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U' R' U R U R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "d' L U' L' U L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U' L' U L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "y U' R U' R' U R U R'", "alternative_algorithms": [], "move_count": 9}}}, {"id": "f2l_15", "name": "Case 15", "case_name": "15", "category": "NxNxN Cube", "difficulty": 2, "description": "F2L case 15", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R' D' R U' R' D R U R U' R'", "alternative_algorithms": [], "move_count": 11}, "Front-Left": {"algorithm": "L' U L U2 F U F'", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "L U L' U2 L U' L' U L U' L'", "alternative_algorithms": [], "move_count": 11}, "Back-Right": {"algorithm": "R' U R U2 f R f'", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_16", "name": "Case 16", "case_name": "16", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 16", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U' R' U2 F' U' F", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "F U' R U' R' U2 F'", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "L U' L' U2 f' L' f", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "R' U' R U2 R' U R U' R' U R", "alternative_algorithms": [], "move_count": 11}}}, {"id": "f2l_17", "name": "Case 17", "case_name": "17", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 17", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U2 R' U' R U R'", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "y L U2 L' U' L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L U2 L' U' L U L'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "y' L U2 L' U' L U L'", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_18", "name": "Case 18", "case_name": "18", "category": "NxNxN Cube", "difficulty": 2, "description": "F2L case 18", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "y' R' U2 R U R' U' R", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "L' U2 L U L' U' L", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "y R' U2 R U R' U' R", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "R' U2 R U R' U' R", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_19", "name": "Case 19", "case_name": "19", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 19", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U R U2 R' U R U' R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U L2 F' L' F L' U L", "alternative_algorithms": [], "move_count": 10}, "Back-Left": {"algorithm": "U L U2 L' U L U' L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "y U R U2 R' U R U' R'", "alternative_algorithms": [], "move_count": 9}}}, {"id": "f2l_20", "name": "Case 20", "case_name": "20", "category": "NxNxN Cube", "difficulty": 2, "description": "F2L case 20", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "y' U' R' U2 R U' R' U R", "alternative_algorithms": [], "move_count": 9}, "Front-Left": {"algorithm": "U' L' U2 L U' L' U L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "y U' R' U2 R U' R' U R", "alternative_algorithms": [], "move_count": 9}, "Back-Right": {"algorithm": "U' R' U2 R U' R' U R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_21", "name": "Case 21", "case_name": "21", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 21", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U2 R U R' U R U' R", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "l' U l U2 l' U' l", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "L U' L' U2 L U L'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "r' U r U2 r' U' r", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_22", "name": "Case 22", "case_name": "22", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 22", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "r U' r' U2 r U r'", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "L' U L U2 L' U' L", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "l U' l' U2 l U l'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "R' U R U2 R' U' R", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_23", "name": "Case 23", "case_name": "23", "category": "NxNxN Cube", "difficulty": 2, "description": "F2L case 23", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U R U' R' U' R U' R' U R U' R'", "alternative_algorithms": [], "move_count": 12}, "Front-Left": {"algorithm": "F' U' L' U L F L' U L", "alternative_algorithms": [], "move_count": 9}, "Back-Left": {"algorithm": "U L U' L' U' L U' L' U L U' L'", "alternative_algorithms": [], "move_count": 12}, "Back-Right": {"algorithm": "U R' F R' F' R2 U' R' U R", "alternative_algorithms": [], "move_count": 10}}}, {"id": "f2l_24", "name": "Case 24", "case_name": "24", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 24", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F U R U' R' F' R U' R'", "alternative_algorithms": [], "move_count": 9}, "Front-Left": {"algorithm": "U' L' U L U L' U L U' L' U L", "alternative_algorithms": [], "move_count": 12}, "Back-Left": {"algorithm": "U2 r U R' U R U2 B r'", "alternative_algorithms": [], "move_count": 9}, "Back-Right": {"algorithm": "R' U' R U2 R' U' R U R' U' R", "alternative_algorithms": [], "move_count": 11}}}, {"id": "f2l_25", "name": "Case 25", "case_name": "25", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 25", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R' F R F' R U R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U' L' U L F' r U r'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L U' L' U' L U' L' U L U L'", "alternative_algorithms": [], "move_count": 11}, "Back-Right": {"algorithm": "U' R' U M U' R U M'", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_26", "name": "Case 26", "case_name": "26", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 26", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U R U' R' F R' F' R", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "r U r' U' r' F r F'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L S L' U L S' L'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "U f R f' U' R' U' R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_27", "name": "Case 27", "case_name": "27", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 27", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U' R' U R U' R'", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "L' U' L U F' r U r'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L U' L' U L U' L'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "R' U2 R' F R F' R", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_28", "name": "Case 28", "case_name": "28", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 28", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R' U' F R' F' R", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "L' U L U' L' U L", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "L U2 L F' L' F L'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "R' U R U' R' U R", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_29", "name": "Case 29", "case_name": "29", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 29", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R' F R F' U R U' R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "L' U' L U L' U' L", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "y R' U' R U R' U' R", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "R' U' R U R' U' R", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_30", "name": "Case 30", "case_name": "30", "category": "NxNxN Cube", "difficulty": 1, "description": "F2L case 30", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R' U' R U R'", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "L F' L' F U' L' U L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L U L' U' L U L'", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "y' L U L' U' L U L'", "alternative_algorithms": [], "move_count": 8}}}]}}}}}}